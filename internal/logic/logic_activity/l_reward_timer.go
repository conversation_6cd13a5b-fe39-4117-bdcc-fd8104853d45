package logic_activity

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// RunAutoRewardTask 执行自动领奖任务
func RunAutoRewardTask() {
	// 获取分布式锁，防止多实例重复执行
	isGet, unlock := dlm.DefaultLockMgr.OptimisticLockKey(config.RDS_LOCK_AUTO_REWARD_TIMER, 300)
	if !isGet {
		logrus.Debug("auto reward task is already running, skipping...")
		return
	}
	defer unlock()

	logrus.Info("starting auto reward task...")

	// todo 初始化上下文 后续读配置或者环境变量
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(int32(commonPB.PRODUCT_ID_PID_FISHER)),
		interceptor.WithChannelType(int32(commonPB.CHANNEL_TYPE_CT_GOOGLE)),
	)

	// 1. 获取所有活动列表
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil || len(allActivities) == 0 {
		logrus.Warnf("no activities found, auto reward task completed")
		return
	}

	// 2. 遍历每个活动
	for activityId, activityCfg := range allActivities {
		err := processActivityAutoReward(ctx, activityId, activityCfg)
		if err != nil {
			logrus.Errorf("RunAutoRewardTask failed to process auto reward for activity %d: %v", activityId, err)
		}
	}
}

// processActivityAutoReward 处理单个活动的自动领奖
func processActivityAutoReward(ctx context.Context, activityId int64, activityCfg *cmodel.Activity) error {
	logrus.Debugf("processing auto reward for activity: %d", activityId)

	// 获取检测的周期
	var targetCycleId int32
	if !activityCfg.IsLoop {
		// 1. 检查活动时间窗口：必须在CloseAt后24小时内
		now := timex.Now().Unix()
		if activityCfg.CloseAt <= 0 {
			logrus.Debugf("activity %d has no close time, skipping auto reward", activityId)
			return nil
		}

		// 检查是否在自动领奖时间窗口内（CloseAt后24小时内）
		autoRewardWindowEnd := activityCfg.CloseAt + 24*3600 // CloseAt后24小时
		if now <= activityCfg.CloseAt {
			logrus.Debugf("activity %d has not closed yet, skipping auto reward: now=%d, closeAt=%d",
				activityId, now, activityCfg.CloseAt)
			return nil
		}
		if now > autoRewardWindowEnd {
			logrus.Debugf("activity %d auto reward window has expired, skipping: now=%d, windowEnd=%d",
				activityId, now, autoRewardWindowEnd)
			return nil
		}
		targetCycleId = 1
	} else {
		currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
		if err != nil {
			return fmt.Errorf("failed to get current cycle for activity %d: %w", activityId, err)
		}
		targetCycleId = currentCycle.CycleId - 2
	}

	if targetCycleId <= 0 {
		logrus.Debugf("no valid target cycle for activity %d auto reward", activityId)
		return nil
	}

	logrus.Infof("calculated auto reward target cycle for activity %d: cycleId=%d", activityId, targetCycleId)

	// 3. 获取目标周期的玩家列表
	players, err := getAllPlayersInCycle(ctx, activityId, targetCycleId)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			logrus.Debugf("no player set found for activity %d cycle %d, skipping", activityId, targetCycleId)
			return nil
		}
		return err
	}

	// 4. 如果Set为空，删除该Set
	if len(players) == 0 {
		err = deleteEmptyPlayerSet(ctx, activityId, targetCycleId)
		if err != nil {
			logrus.Errorf("failed to delete empty player set for activity %d cycle %d: %v", activityId, targetCycleId, err)
		}
		logrus.Debugf("deleted empty player set for activity %d cycle %d", activityId, targetCycleId)
		return nil
	}

	// 5. 为每个玩家执行自动领奖
	successCount := 0
	for _, playerId := range players {
		err = processPlayerAutoReward(ctx, playerId, activityId, targetCycleId, activityCfg)
		if err != nil {
			logrus.Errorf("failed to process auto reward for player %d in activity %d cycle %d: %v",
				playerId, activityId, targetCycleId, err)
		} else {
			successCount++
		}
	}

	logrus.Infof("auto reward processed for activity %d cycle %d: total=%d, success=%d",
		activityId, targetCycleId, len(players), successCount)

	return nil
}

// getAllPlayersInCycle 获取周期内的所有玩家
func getAllPlayersInCycle(ctx context.Context, activityId int64, cycleId int32) ([]uint64, error) {
	// 先检查Set是否存在
	count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return []uint64{}, nil
	}

	// 分批获取所有玩家，避免一次性获取过多数据
	const batchSize = 1000
	var allPlayers []uint64

	for {
		players, err := dao_activity.GetPlayersInCycle(ctx, activityId, cycleId, batchSize)
		if err != nil {
			return nil, err
		}
		if len(players) == 0 {
			break
		}

		allPlayers = append(allPlayers, players...)

		// 如果返回的数量小于批次大小，说明已经获取完所有数据
		if len(players) < batchSize {
			break
		}
	}

	return allPlayers, nil
}

// deleteEmptyPlayerSet 删除空的玩家Set
func deleteEmptyPlayerSet(ctx context.Context, activityId int64, cycleId int32) error {
	key := config.ActivityPlayersKey(activityId, cycleId)
	return dao_activity.DeletePlayerCycleSet(ctx, key)
}

// processPlayerAutoReward 为单个玩家处理自动领奖
func processPlayerAutoReward(ctx context.Context, playerId uint64, activityId int64, cycleId int32, activityCfg *cmodel.Activity) error {
	// 创建领奖请求
	req := &activityPB.ClaimActivityRewardReq{
		ActivityId: commonPB.ACTIVITY_TYPE(activityId),
		CycleId:    cycleId,
	}

	// 调用现有的领奖逻辑
	logic := NewActivityLogic()
	_, err := logic.ClaimReward(ctx, playerId, req)

	// 如果没有可领取的奖励，这不算错误
	if err != nil && err.Error() == "no reward stages available for claim currently" {
		logrus.Debugf("no rewards available for player %d in activity %d cycle %d", playerId, activityId, cycleId)
		// 即使没有奖励可领取，也要从Set中移除玩家
		_ = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)
		return nil
	}

	return err
}
