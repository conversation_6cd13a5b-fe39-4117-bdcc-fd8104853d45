# 活动自动领奖功能增强文档

## 概述

本文档描述了对 `processActivityAutoReward` 函数的增强修改，实现了基于活动结束时间的自动领奖逻辑。

## 修改内容

### 1. 主要变更

#### 原有逻辑
- 处理"上上个周期"（当前周期ID - 2）的自动领奖
- 基于当前周期计算目标周期

#### 新逻辑
- 基于活动 `CloseAt` 时间的24小时窗口触发自动领奖
- 根据活动类型（循环/非循环）计算正确的目标周期ID
- 更精确的时间窗口控制

### 2. 核心功能

#### 时间窗口检查
```go
// 检查是否在自动领奖时间窗口内（CloseAt后24小时内）
autoRewardWindowEnd := activityCfg.CloseAt + 24*3600 // CloseAt后24小时
if now <= activityCfg.CloseAt {
    // 活动未结束，跳过
    return nil
}
if now > autoRewardWindowEnd {
    // 超出24小时窗口，跳过
    return nil
}
```

#### 周期ID计算
```go
func calculateAutoRewardCycleId(activityCfg *cmodel.Activity) (int32, error) {
    // 非循环活动：周期固定为1
    if !activityCfg.IsLoop {
        return 1, nil
    }
    
    // 循环活动：根据CloseAt计算最后一个周期
    cycleSeconds := int64(activityCfg.CycleDays) * 24 * 3600
    elapsedSeconds := activityCfg.CloseAt - activityCfg.OpenAt
    lastCycleId := int32(elapsedSeconds/cycleSeconds) + 1
    
    return lastCycleId, nil
}
```

### 3. 处理逻辑

#### 活动类型处理
1. **非循环活动** (`IsLoop = false`)
   - 周期ID固定为 1
   - 整个活动期间只有一个周期

2. **循环活动** (`IsLoop = true`)
   - 根据 `CycleDays` 计算周期数
   - 使用 `CloseAt` 时间计算最后一个有效周期
   - 处理周期被截断的情况

#### 时间窗口控制
- **触发条件**: 当前时间 > CloseAt 且 <= CloseAt + 24小时
- **跳过条件**: 
  - 活动无 CloseAt 时间
  - 活动未结束
  - 超出24小时窗口

### 4. 错误处理和日志

#### 详细日志记录
```go
logrus.Infof("activity %d is in auto reward window: closeAt=%d, now=%d, windowEnd=%d", 
    activityId, activityCfg.CloseAt, now, autoRewardWindowEnd)

logrus.Infof("calculated auto reward target cycle for activity %d: cycleId=%d", 
    activityId, targetCycleId)
```

#### 错误处理
- 无效的周期天数配置
- 周期计算失败
- Redis数据访问错误
- 玩家领奖处理错误

### 5. Redis数据一致性

#### TTL策略保持不变
- 使用现有的TTL计算逻辑
- 确保数据在合适的时间过期

#### 避免重复领奖
- 复用现有的 `ClaimReward` 逻辑
- 利用已有的防重复机制
- 成功处理后从Redis Set中移除玩家

## 测试验证

### 单元测试
创建了 `test/auto_reward_test.go` 包含：

1. **时间窗口测试**
   - 活动未结束
   - 刚结束（窗口内）
   - 窗口边界
   - 超出窗口
   - 无结束时间

2. **周期计算测试**
   - 非循环活动
   - 循环活动（单周期）
   - 循环活动（多周期）
   - 无效配置

### 编译验证
```bash
go build ./cmd/main.go  # 编译成功
go test ./test/auto_reward_test.go -v  # 测试通过
```

## 兼容性

### 向后兼容
- 保持现有函数签名不变
- 复用现有的DAO和模型
- 不影响其他模块

### 配置要求
- 活动必须有有效的 `CloseAt` 时间
- 循环活动需要有效的 `CycleDays` 配置

## 部署注意事项

1. **定时任务频率**: 建议保持每小时执行一次
2. **监控**: 关注自动领奖的成功率和错误日志
3. **数据清理**: 确保过期的Redis数据能正常清理
4. **性能**: 大量玩家的活动可能需要分批处理

## 未来优化建议

1. **分批处理**: 对于大量玩家的活动，可以考虑分批处理
2. **重试机制**: 对失败的玩家可以实现重试逻辑
3. **监控指标**: 添加更多的监控指标和告警
4. **配置化**: 将24小时窗口配置化，支持不同活动的不同窗口期
